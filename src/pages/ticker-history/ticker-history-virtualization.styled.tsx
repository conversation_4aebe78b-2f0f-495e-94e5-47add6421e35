import styled from 'styled-components'
import { Table } from '@mz-codes/design-system'

export const VirtualScrollContainer = styled.div<{ maxHeight: number }>`
  max-height: ${({ maxHeight }) => maxHeight}px;
  overflow-y: auto;
  overflow-x: hidden;
  /* Reserva espaço para a scrollbar para manter alinhamento */
  scrollbar-gutter: stable;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
`

export const VirtualContentContainer = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`

export const VirtualItemsWrapper = styled.div<{ translateY: number }>`
  transform: translateY(${({ translateY }) => translateY}px);
`

export const VirtualizedTableRow = styled(Table.TR) <{ itemHeight: number }>`
  height: ${({ itemHeight }) => itemHeight}px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`

export const VirtualizedTable = styled.table`
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin: 0;

  td {
    /* Garante que as colunas tenham exatamente a mesma largura que o header */
    width: 16.666%; /* 100% / 6 colunas */
    padding: 12px 16px;
    text-align: left;
    font-size: 14px;
    font-weight: 400;
    color: inherit;
    vertical-align: middle;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`

// Wrapper principal que controla o layout da tabela
export const TableWrapper = styled.div`
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
  background-color: ${({ theme }) => theme.legacy.colors.neutral.contentBackground};
`

// Header fixo da tabela
export const TableHeaderWrapper = styled.div`
  width: 100%;
  background-color: ${({ theme }) => theme.legacy.colors.neutral.contentBackground};
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  /* Força o header a ter exatamente a mesma largura que o container de scroll */
  overflow: hidden;

  table {
    width: 100%;
    margin: 0;

    /* Remove margens e paddings que podem causar desalinhamento */
    thead {
      display: table-header-group;
      width: 100%;

      tr {
        display: table-row;
        width: 100%;

        th {
          /* Garante que as colunas tenham largura fixa e proporcional */
          width: 16.666%; /* 100% / 6 colunas */
          box-sizing: border-box;
          padding: 20px 16px;
          text-align: left;
        }
      }
    }
  }
`

// Container do corpo da tabela com scroll
export const TableBodyWrapper = styled.div`
  width: 100%;
  position: relative;
`

export const CenteredLoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  width: 100%;
`
