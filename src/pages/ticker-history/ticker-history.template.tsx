import { Buttons, New<PERSON><PERSON><PERSON><PERSON>, Dropdown, Loading, Header, Table } from '@mz-codes/design-system'
import { Page, PageContent, DataNotFound } from 'components'
import { i18n } from 'translate'
import { TPriceHistory } from 'hooks'
import { TTickerHistoryTemplate } from './ticker-history.types'
import { useVirtualization } from './ticker-history-virtualization.hook'
import {
  VirtualScrollContainer,
  VirtualContentContainer,
  VirtualItemsWrapper,
  VirtualizedTableRow,
  VirtualizedTable,
  CenteredLoadingContainer,
  TableWrapper,
  TableHeaderWrapper,
  TableBodyWrapper,
} from './ticker-history-virtualization.styled'

// Virtual scrolling implementation for large datasets
function VirtualizedTableBody({
  items,
  itemHeight = 45,
  maxVisibleItems = 50,
}: {
  items: TPriceHistory[]
  itemHeight?: number
  maxVisibleItems?: number
}) {
  const virtualization = useVirtualization(items, { itemHeight, maxVisibleItems })
  const { state, actions } = virtualization

  if (!state.shouldVirtualize) {
    return (
      <VirtualizedTable>
        <tbody>
          {items.map((tickerPrice, index) => {
            const { date, high, low, open, close, volume } = tickerPrice
            const key = `${date}-${index}`
            return (
              <VirtualizedTableRow key={key} itemHeight={itemHeight}>
                <td>{date}</td>
                <td>{open}</td>
                <td>{close}</td>
                <td>{low}</td>
                <td>{high}</td>
                <td>{volume}</td>
              </VirtualizedTableRow>
            )
          })}
        </tbody>
      </VirtualizedTable>
    )
  }

  return (
    <VirtualScrollContainer maxHeight={state.containerMaxHeight} onScroll={actions.handleScroll}>
      <VirtualContentContainer totalHeight={state.contentTotalHeight}>
        <VirtualItemsWrapper translateY={state.itemsWrapperTranslateY}>
          <VirtualizedTable>
            <tbody>
              {state.visibleItems.map((tickerPrice, index) => {
                const { date, high, low, open, close, volume } = tickerPrice
                const key = `${date}-${index}`
                return (
                  <VirtualizedTableRow key={key} itemHeight={itemHeight}>
                    <td>{date}</td>
                    <td>{open}</td>
                    <td>{close}</td>
                    <td>{low}</td>
                    <td>{high}</td>
                    <td>{volume}</td>
                  </VirtualizedTableRow>
                )
              })}
            </tbody>
          </VirtualizedTable>
        </VirtualItemsWrapper>
      </VirtualContentContainer>
    </VirtualScrollContainer>
  )
}

export function TickerHistoryTemplate(props: TTickerHistoryTemplate) {
  const {
    translations,
    handleTickerValue,
    currentTicker,
    tickerOptions = [],
    handleStartDateValue,
    selectedStartDate,
    selectedEndDate,
    handleEndDateValue,
    onExportClick,
    isLoading,
    listItems,
  } = props

  return (
    <Page>
      <Header>
        <Header.Content>
          <Header.Item>
            <Header.Label>{translations.tickerLabel}</Header.Label>
            <Dropdown options={tickerOptions} selected={currentTicker} onChange={handleTickerValue} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.startDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedStartDate}
              onChange={handleStartDateValue}
              maxDate={selectedEndDate}
              hint={i18n.t('startDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.endDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedEndDate}
              onChange={handleEndDateValue}
              minDate={selectedStartDate}
              hint={i18n.t('endDate')}
            />
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup style={{ justifyContent: 'center' }}>
          <Buttons.Export onClick={onExportClick}>{translations.exportButton as string}</Buttons.Export>
        </Header.ButtonGroup>
      </Header>
      <PageContent>
        {isLoading && (
          <CenteredLoadingContainer>
            <Loading />
          </CenteredLoadingContainer>
        )}
        {!isLoading && (
          <>
            {listItems.length === 0 && <DataNotFound>{translations.dataNotFound}</DataNotFound>}
            {listItems.length !== 0 && (
              <TableWrapper>
                <TableHeaderWrapper>
                  <Table>
                    <Table.THead>
                      <Table.TR>
                        <Table.TH>{translations.tableTitleDate as string}</Table.TH>
                        <Table.TH>{translations.tableTitleOpeningPrice as string}</Table.TH>
                        <Table.TH>{translations.tableTitleClosingPrice as string}</Table.TH>
                        <Table.TH>{translations.tableTitleLowPrice as string}</Table.TH>
                        <Table.TH>{translations.tableTitleHighPrice as string}</Table.TH>
                        <Table.TH>{translations.tableTitleVolume as string}</Table.TH>
                      </Table.TR>
                    </Table.THead>
                  </Table>
                </TableHeaderWrapper>
                <TableBodyWrapper>
                  <VirtualizedTableBody items={listItems} itemHeight={45} maxVisibleItems={20} />
                </TableBodyWrapper>
              </TableWrapper>
            )}
          </>
        )}
      </PageContent>
    </Page>
  )
}
