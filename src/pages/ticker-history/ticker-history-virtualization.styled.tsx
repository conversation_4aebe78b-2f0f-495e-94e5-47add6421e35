import styled from 'styled-components'
import { Table } from '@mz-codes/design-system'

export const VirtualScrollContainer = styled.div<{ maxHeight: number }>`
  max-height: ${({ maxHeight }) => maxHeight}px;
  overflow-y: auto;
  overflow-x: hidden;
  /* Reserva espaço para a scrollbar para manter alinhamento */
  scrollbar-gutter: stable;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
`

export const VirtualContentContainer = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`

export const VirtualItemsWrapper = styled.div<{ translateY: number }>`
  transform: translateY(${({ translateY }) => translateY}px);
`

export const VirtualizedTableRow = styled(Table.TR) <{ itemHeight: number }>`
  height: ${({ itemHeight }) => itemHeight}px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`

export const VirtualizedTable = styled.table`
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;

  td {
    padding: 12px 16px;
    text-align: left;
    font-size: 14px;
    font-weight: 400;
    color: inherit;
    vertical-align: middle;
  }
`

// Wrapper para garantir alinhamento entre header e corpo da tabela
export const TableWrapper = styled.div`
  width: 100%;
  overflow: hidden;
`

// Header da tabela com espaço reservado para scrollbar
export const TableHeaderWrapper = styled.div<{ scrollbarWidth: number }>`
  width: 100%;
  box-sizing: border-box;

  /* Reserva espaço para a scrollbar no header para manter alinhamento */
  padding-right: ${({ scrollbarWidth }) => scrollbarWidth}px;

  /* Usa scrollbar-gutter como fallback para navegadores modernos */
  scrollbar-gutter: stable;
`

export const CenteredLoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  width: 100%;
`
