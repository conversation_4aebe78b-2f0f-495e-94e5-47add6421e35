import { AxiosProgressEvent } from 'axios'
import { BaseError, UploadBaseError } from 'errors'
import { api, MZ_IRM_NEW } from 'globals/api'

type TPostUploadShareholderBaseProps = {
  file: File
  companyId: string
  tickerId: string
  onUploadProgress?(progressEvent: AxiosProgressEvent): void
}

type ResponseData = {
  success: boolean
  data: unknown
}

const postUploadShareholderBase = async ({
  file,
  companyId = '',
  tickerId = '',
  onUploadProgress,
}: TPostUploadShareholderBaseProps) => {
  try {
    const payload = new FormData()
    payload.append('shareholderBase', file, file.name)
    payload.append('tickerId', tickerId)
    payload.append('notify', 'true')

    const config = { onUploadProgress }
    const response = await api.post<ResponseData>(
      `${MZ_IRM_NEW}/position/companies/${companyId}/position-batch/upload`,
      payload,
      config
    )

    return response.data
  } catch (err) {
    if (err instanceof BaseError) throw err
    throw new UploadBaseError()
  }
}

export { postUploadShareholderBase }
