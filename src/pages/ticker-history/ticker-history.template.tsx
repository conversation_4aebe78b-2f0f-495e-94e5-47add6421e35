import { Buttons, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dropdown, Loading, Header } from '@mz-codes/design-system'
import { Page, PageContent, DataNotFound } from 'components'
import { i18n } from 'translate'
import { TPriceHistory } from 'hooks'
import { TTickerHistoryTemplate } from './ticker-history.types'
import { useVirtualization } from './ticker-history-virtualization.hook'
import {
  VirtualScrollContainer,
  VirtualContentContainer,
  VirtualItemsWrapper,
  VirtualizedTableRow,
  VirtualizedTable,
  VirtualizedTableCell,
  CenteredLoadingContainer,
  TableWrapper,
  TableHeaderWrapper,
  TableHeaderCell,
  TableBodyWrapper,
} from './ticker-history-virtualization.styled'

// Virtual scrolling implementation for large datasets
function VirtualizedTableBody({
  items,
  itemHeight = 45,
  maxVisibleItems = 50,
}: {
  items: TPriceHistory[]
  itemHeight?: number
  maxVisibleItems?: number
}) {
  const virtualization = useVirtualization(items, { itemHeight, maxVisibleItems })
  const { state, actions } = virtualization

  if (!state.shouldVirtualize) {
    return (
      <VirtualizedTable>
        {items.map((tickerPrice, index) => {
          const { date, high, low, open, close, volume } = tickerPrice
          const key = `${date}-${index}`
          return (
            <VirtualizedTableRow key={key} itemHeight={itemHeight}>
              <VirtualizedTableCell>{date}</VirtualizedTableCell>
              <VirtualizedTableCell>{open}</VirtualizedTableCell>
              <VirtualizedTableCell>{close}</VirtualizedTableCell>
              <VirtualizedTableCell>{low}</VirtualizedTableCell>
              <VirtualizedTableCell>{high}</VirtualizedTableCell>
              <VirtualizedTableCell>{volume}</VirtualizedTableCell>
            </VirtualizedTableRow>
          )
        })}
      </VirtualizedTable>
    )
  }

  return (
    <VirtualScrollContainer maxHeight={state.containerMaxHeight} onScroll={actions.handleScroll}>
      <VirtualContentContainer totalHeight={state.contentTotalHeight}>
        <VirtualItemsWrapper translateY={state.itemsWrapperTranslateY}>
          <VirtualizedTable>
            {state.visibleItems.map((tickerPrice, index) => {
              const { date, high, low, open, close, volume } = tickerPrice
              const key = `${date}-${index}`
              return (
                <VirtualizedTableRow key={key} itemHeight={itemHeight}>
                  <VirtualizedTableCell>{date}</VirtualizedTableCell>
                  <VirtualizedTableCell>{open}</VirtualizedTableCell>
                  <VirtualizedTableCell>{close}</VirtualizedTableCell>
                  <VirtualizedTableCell>{low}</VirtualizedTableCell>
                  <VirtualizedTableCell>{high}</VirtualizedTableCell>
                  <VirtualizedTableCell>{volume}</VirtualizedTableCell>
                </VirtualizedTableRow>
              )
            })}
          </VirtualizedTable>
        </VirtualItemsWrapper>
      </VirtualContentContainer>
    </VirtualScrollContainer>
  )
}

export function TickerHistoryTemplate(props: TTickerHistoryTemplate) {
  const {
    translations,
    handleTickerValue,
    currentTicker,
    tickerOptions = [],
    handleStartDateValue,
    selectedStartDate,
    selectedEndDate,
    handleEndDateValue,
    onExportClick,
    isLoading,
    listItems,
  } = props

  return (
    <Page>
      <Header>
        <Header.Content>
          <Header.Item>
            <Header.Label>{translations.tickerLabel}</Header.Label>
            <Dropdown options={tickerOptions} selected={currentTicker} onChange={handleTickerValue} />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.startDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedStartDate}
              onChange={handleStartDateValue}
              maxDate={selectedEndDate}
              hint={i18n.t('startDate')}
            />
          </Header.Item>
          <Header.Item>
            <Header.Label>{translations.endDateLabel as string}</Header.Label>
            <NewDatepicker
              lang={i18n.language}
              selected={selectedEndDate}
              onChange={handleEndDateValue}
              minDate={selectedStartDate}
              hint={i18n.t('endDate')}
            />
          </Header.Item>
        </Header.Content>
        <Header.ButtonGroup style={{ justifyContent: 'center' }}>
          <Buttons.Export onClick={onExportClick}>{translations.exportButton as string}</Buttons.Export>
        </Header.ButtonGroup>
      </Header>
      <PageContent>
        {isLoading && (
          <CenteredLoadingContainer>
            <Loading />
          </CenteredLoadingContainer>
        )}
        {!isLoading && (
          <>
            {listItems.length === 0 && <DataNotFound>{translations.dataNotFound}</DataNotFound>}
            {listItems.length !== 0 && (
              <TableWrapper>
                <TableHeaderWrapper>
                  <TableHeaderCell>{translations.tableTitleDate as string}</TableHeaderCell>
                  <TableHeaderCell>{translations.tableTitleOpeningPrice as string}</TableHeaderCell>
                  <TableHeaderCell>{translations.tableTitleClosingPrice as string}</TableHeaderCell>
                  <TableHeaderCell>{translations.tableTitleLowPrice as string}</TableHeaderCell>
                  <TableHeaderCell>{translations.tableTitleHighPrice as string}</TableHeaderCell>
                  <TableHeaderCell>{translations.tableTitleVolume as string}</TableHeaderCell>
                </TableHeaderWrapper>
                <TableBodyWrapper>
                  <VirtualizedTableBody items={listItems} itemHeight={45} maxVisibleItems={20} />
                </TableBodyWrapper>
              </TableWrapper>
            )}
          </>
        )}
      </PageContent>
    </Page>
  )
}
