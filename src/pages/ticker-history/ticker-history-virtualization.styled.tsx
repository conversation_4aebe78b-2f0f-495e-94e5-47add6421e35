import styled from 'styled-components'

export const VirtualScrollContainer = styled.div<{ maxHeight: number }>`
  max-height: ${({ maxHeight }) => maxHeight}px;
  overflow-y: auto;
  overflow-x: hidden; /* Força esconder a barra horizontal */
  width: 100%;

  /* Remove qualquer possibilidade de scroll horizontal */
  box-sizing: border-box;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
`

export const VirtualContentContainer = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`

export const VirtualItemsWrapper = styled.div<{ translateY: number }>`
  transform: translateY(${({ translateY }) => translateY}px);
`

export const VirtualizedTableRow = styled.div<{ itemHeight: number }>`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; /* 6 colunas iguais, igual ao header */
  height: ${({ itemHeight }) => itemHeight}px;
  border-bottom: 1px solid #344b69; /* Mesma cor da borda do header */
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  /* Garante que não ultrapasse o container */
  max-width: 100%;

  &:hover {
    background: radial-gradient(
      52.96% 3704.62% at 50% 50%,
      rgba(35, 40, 56, 0.75) 59.9%,
      rgba(35, 40, 56, 0.376) 80.21%,
      rgba(35, 40, 56, 0) 100%
    );
  }

  &:last-child {
    border-bottom: none;
  }
`

export const VirtualizedTableCell = styled.div`
  padding: 20px;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  color: ${({ theme }) => theme.legacy.colors.grayScale.texts};
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`

export const VirtualizedTable = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  max-width: 100%;
`

export const TableWrapper = styled.div`
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  border: none;
  border-radius: 0;
  overflow: hidden;
  background-color: transparent;
  margin-right: 20px;
`

export const TableHeaderWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
  background-color: ${({ theme }) => theme.legacy.colors.neutral.contentBackground};
  border-bottom: 1px solid #344b69;
  width: 100%;
  box-sizing: border-box;
  /* Remove padding para que as bordas cheguem até o final */
`

export const TableHeaderCell = styled.div`
  padding: 20px;
  color: #7fc5fb;
  font-weight: 700;
  font-size: 14px;
  text-align: left;
  box-sizing: border-box;
  border: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`

export const TableBodyWrapper = styled.div`
  width: 100%;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  /* Garante que não cause scroll horizontal */
  max-width: 100%;
`

export const CenteredLoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  width: 100%;
`
