import { useTickerHistory } from './ticker-history.hook'
import { TickerHistoryTemplate } from './ticker-history.template'
import { translations } from './ticker-history.translations'

export function TickerHistory() {
  const {
    currentTicker,
    tickerOptions,
    listItems,
    selectedStartDate,
    selectedEndDate,
    isLoading,
    handleTickerValue,
    handleStartDateValue,
    handleEndDateValue,
    onExportClick,
  } = useTickerHistory()

  return (
    <TickerHistoryTemplate
      translations={translations}
      isLoading={isLoading}
      listItems={listItems}
      tickerOptions={tickerOptions}
      handleTickerValue={handleTickerValue}
      currentTicker={currentTicker}
      handleStartDateValue={handleStartDateValue}
      selectedStartDate={selectedStartDate}
      handleEndDateValue={handleEndDateValue}
      selectedEndDate={selectedEndDate}
      onExportClick={onExportClick}
    />
  )
}
