import { TOption } from '@mz-codes/design-system'
import { TPriceHistory } from 'hooks'
import { ticker } from 'globals/services/tickers'

export interface VirtualizationConfig {
  itemHeight: number
  maxVisibleItems: number
}

export interface VirtualizationState {
  startIndex: number
  visibleItems: TPriceHistory[]
  shouldVirtualize: boolean
  containerMaxHeight: number
  contentTotalHeight: number
  itemsWrapperTranslateY: number
}

export interface VirtualizationActions {
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void
}

export interface UseVirtualizationReturn {
  state: VirtualizationState
  actions: VirtualizationActions
}

export type TTickerHistoryPageTranslations = {
  dataNotFound: string
  startDateLabel: string
  endDateLabel: string
  tickerLabel: string
  exportButton: string
  exportSuccess: string
  exportMessage: string
  tableTitleDate: string
  tableTitleOpeningPrice: string
  tableTitleClosingPrice: string
  tableTitleLowPrice: string
  tableTitleHighPrice: string
  tableTitleVolume: string
}

export type TTranslations = TTickerHistoryPageTranslations

export type TTickerHistoryTemplate = {
  translations: TTranslations
  isLoading: boolean
  currentTicker?: TOption
  tickerOptions?: TOption[]
  handleTickerValue: (option: TOption) => void
  handleStartDateValue: (date: Date | null) => void
  selectedStartDate: Date
  handleEndDateValue: (date: Date | null) => void
  selectedEndDate: Date
  onExportClick: () => void
  listItems: TPriceHistory[]
}

export type TTickerHistoryHookReturn = {
  tickers: ticker[]
  currentTicker?: TOption
  tickerOptions?: TOption[]
  listItems: TPriceHistory[]
  selectedStartDate: Date
  selectedEndDate: Date
  isLoading: boolean
  currentLanguage: string
  handleTickerValue: (ticker: TOption) => void
  handleStartDateValue: (date: Date | null) => void
  handleEndDateValue: (date: Date | null) => void
  onExportClick: () => Promise<void>
}
