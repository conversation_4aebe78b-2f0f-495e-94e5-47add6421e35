import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { subDays } from 'date-fns'
import { getCompany } from 'globals/storages/locals'
import { getTickers, ticker } from 'globals/services/tickers'
import { getSerializedTickerPriceHistory, TPriceHistory, postTickerPriceHistoryExport } from 'hooks'
import { GoToHistoryButton } from 'components'
import { TOption, useToast } from '@mz-codes/design-system'
import { i18n } from 'translate'
import { formatDateToString } from 'utils'
import { BaseError, SelectedTickerNotFound } from 'errors'
import { TTickerHistoryHookReturn } from './ticker-history.types'

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
const MAX_CACHE_SIZE = 20

// Cache entry interface with LRU support
interface CacheEntry {
  data: TPriceHistory[]
  timestamp: number
  lastAccessed: number
}

// LRU Cache implementation
class LRUCache {
  private cache = new Map<string, CacheEntry>()

  private maxSize: number

  constructor(maxSize: number = MAX_CACHE_SIZE) {
    this.maxSize = maxSize
  }

  get(key: string): CacheEntry | undefined {
    const entry = this.cache.get(key)
    if (entry) {
      entry.lastAccessed = Date.now()
      return entry
    }
    return undefined
  }

  set(key: string, value: Omit<CacheEntry, 'lastAccessed'>): void {
    const entry: CacheEntry = {
      ...value,
      lastAccessed: Date.now(),
    }

    this.cache.set(key, entry)

    if (this.cache.size > this.maxSize) {
      this.evictLeastRecentlyUsed()
    }
  }

  private evictLeastRecentlyUsed(): void {
    const entries = Array.from(this.cache.entries())
    const oldestEntry = entries.reduce(
      (oldest, [key, entry]) => {
        if (entry.lastAccessed < oldest.lastAccessed) {
          return { key, lastAccessed: entry.lastAccessed }
        }
        return oldest
      },
      { key: '', lastAccessed: Infinity }
    )

    if (oldestEntry.key) {
      this.cache.delete(oldestEntry.key)
    }
  }

  clear(): void {
    this.cache.clear()
  }

  cleanup(): void {
    const now = Date.now()
    const expiredKeys = Array.from(this.cache.entries())
      .filter(([, entry]) => now - entry.timestamp > CACHE_DURATION)
      .map(([key]) => key)

    expiredKeys.forEach((key) => this.cache.delete(key))
  }

  size(): number {
    return this.cache.size
  }
}

// Debounce utility
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  const timerRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (timerRef.current) clearTimeout(timerRef.current)
    timerRef.current = setTimeout(() => setDebouncedValue(value), delay)
    return () => {
      if (timerRef.current) clearTimeout(timerRef.current)
    }
  }, [value, delay])

  return debouncedValue
}

export function useTickerHistory(): TTickerHistoryHookReturn {
  const company = getCompany()
  const currentLanguage = i18n.language.startsWith('pt') ? 'pt-BR' : 'en-US'
  const startDate = subDays(new Date(), 7)
  const endDate = subDays(new Date(), 1)

  // Isolated cache instance for this hook
  const cacheRef = useRef<LRUCache>(new LRUCache())
  const lastCompanyIdRef = useRef<string>(company.id)

  // Cache invalidation when company changes
  useEffect(() => {
    if (lastCompanyIdRef.current !== company.id) {
      cacheRef.current.clear()
      lastCompanyIdRef.current = company.id
    }
  }, [company.id])

  // Periodic cache cleanup
  useEffect(() => {
    const interval = setInterval(() => {
      cacheRef.current.cleanup()
    }, CACHE_DURATION)

    return () => clearInterval(interval)
  }, [])

  const { createToast } = useToast()
  const [tickers, setTickers] = useState<ticker[]>([])
  const [currentTicker, setCurrentTicker] = useState<TOption>()
  const [tickerOptions, setTickerOptions] = useState<TOption[]>()
  const [selectedStartDate, setSelectedStartDate] = useState(startDate)
  const [selectedEndDate, setSelectedEndDate] = useState(endDate)
  const [listItems, setListItems] = useState<TPriceHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Memoized ticker selection for performance
  const selectedTicker = useMemo(
    () => tickers.find((tickerParam) => tickerParam.tickerId === currentTicker?.value),
    [tickers, currentTicker?.value]
  )

  // Debounced date changes to prevent excessive API calls
  const debouncedStartDate = useDebounce(selectedStartDate, 500)
  const debouncedEndDate = useDebounce(selectedEndDate, 500)

  // Memoized ticker processing for performance
  const handleGetTickers = useCallback(async () => {
    const response = await getTickers(company.id)

    const tickersFiltered = response?.filter((item) => item.label !== 'TOTAL')

    const options = tickersFiltered.map<TOption>((item) => ({
      label: item.label,
      value: item.tickerId,
    }))
    setTickers(tickersFiltered)
    setCurrentTicker(options[0])
    setTickerOptions(options)
  }, [company.id])

  useEffect(() => {
    handleGetTickers()
  }, [handleGetTickers])

  // Optimized data loading with intelligent caching
  const handleLoadTickerPriceHistory = useCallback(async () => {
    setIsLoading(true)
    try {
      if (!selectedTicker) throw new SelectedTickerNotFound()

      // Create cache key based on ticker and date range
      const cacheKey = `${selectedTicker.xigniteTicker}-${formatDateToString(debouncedStartDate)}-${formatDateToString(debouncedEndDate)}`
      const cachedData = cacheRef.current.get(cacheKey)
      const now = Date.now()

      // Check if cached data is still valid
      if (cachedData && now - cachedData.timestamp < CACHE_DURATION) {
        setListItems(cachedData.data)
        setIsLoading(false)
        return
      }

      // Fetch new data if not cached or expired
      const data = await getSerializedTickerPriceHistory({
        ticker: selectedTicker.xigniteTicker,
        startDate: formatDateToString(debouncedStartDate),
        endDate: formatDateToString(debouncedEndDate),
      })

      // Cache the data with timestamp (LRU automatically handles size limit)
      cacheRef.current.set(cacheKey, { data, timestamp: now })

      setListItems(data)
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 20000,
          type: 'error',
        })
        return
      }
      createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        duration: 20000,
        type: 'error',
      })
    } finally {
      setIsLoading(false)
    }
  }, [createToast, debouncedEndDate, debouncedStartDate, selectedTicker])

  // Use debounced dates to prevent excessive API calls
  useEffect(() => {
    if (!currentTicker?.value) return
    handleLoadTickerPriceHistory()
  }, [currentTicker, debouncedStartDate, debouncedEndDate, handleLoadTickerPriceHistory])

  const handleTickerValue = (selectedTickerParam: TOption) => {
    if (currentTicker?.value === selectedTickerParam.value) return
    setCurrentTicker(selectedTickerParam)
  }

  // Optimized date handlers with validation
  const handleStartDateValue = useCallback(
    (value: Date | null) => {
      if (!value || value === selectedStartDate) return
      setSelectedStartDate(value)
    },
    [selectedStartDate]
  )

  const handleEndDateValue = useCallback(
    (value: Date | null) => {
      if (!value || value === selectedEndDate) return
      setSelectedEndDate(value)
    },
    [selectedEndDate]
  )

  const onExportClick = async () => {
    try {
      if (!selectedTicker) throw new SelectedTickerNotFound()

      await postTickerPriceHistoryExport({
        companyId: company.id,
        companyName: company.displayName,
        tickerId: selectedTicker.tickerId,
        ticker: selectedTicker.xigniteTicker,
        stockType: selectedTicker.label.toString(),
        startDate: formatDateToString(selectedStartDate),
        endDate: formatDateToString(selectedEndDate),
        language: currentLanguage,
      })
      createToast({
        title: i18n.t('tickerPriceHistory.success'),
        description: i18n.t('tickerPriceHistory.exportMessage'),
        duration: 9000,
        type: 'success',
        buttons: <GoToHistoryButton />,
      })
    } catch (err: unknown) {
      if (err instanceof BaseError) {
        createToast({
          title: err.title,
          description: err.message,
          duration: 9000,
          type: 'error',
        })
        return
      }
      createToast({
        title: i18n.t('globals.errors.requestFail.title'),
        description: i18n.t('globals.errors.requestFail.message'),
        duration: 9000,
        type: 'error',
      })
    }
  }

  console.log('listItems', listItems)

  const mock = [
    {
      date: '07/14/2025',
      close: '73.64',
      variation: -0.88,
      open: '73.99',
      high: '75.13',
      low: '73.01',
      average: 73.94,
      trades: 24665,
      volume: '7,901,500',
      financialVolume: 584313728,
      provider: 'enfoque',
    },
    {
      date: '07/11/2025',
      close: '74.30',
      variation: -1.35,
      open: '75.30',
      high: '75.37',
      low: '72.90',
      average: 74.2,
      trades: 30641,
      volume: '10,685,700',
      financialVolume: 792934144,
      provider: 'enfoque',
    },
    {
      date: '07/10/2025',
      close: '75.32',
      variation: -3.69,
      open: '72.55',
      high: '76.28',
      low: '71.63',
      average: 73.97,
      trades: 88796,
      volume: '23,906,000',
      financialVolume: **********,
      provider: 'enfoque',
    },
    {
      date: '07/09/2025',
      close: '78.21',
      variation: -3.62,
      open: '80.91',
      high: '83.10',
      low: '77.10',
      average: 79.52,
      trades: 47955,
      volume: '14,961,200',
      financialVolume: **********,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
    {
      date: '07/08/2025',
      close: '81.15',
      variation: -0.7,
      open: '81.85',
      high: '83.55',
      low: '80.84',
      average: 81.75,
      trades: 20621,
      volume: '4,518,200',
      financialVolume: 369381792,
      provider: 'enfoque',
    },
  ]

  return {
    tickers,
    currentTicker,
    tickerOptions,
    listItems: mock,
    selectedStartDate,
    selectedEndDate,
    isLoading,
    currentLanguage,
    handleTickerValue,
    handleStartDateValue,
    handleEndDateValue,
    onExportClick,
  }
}
