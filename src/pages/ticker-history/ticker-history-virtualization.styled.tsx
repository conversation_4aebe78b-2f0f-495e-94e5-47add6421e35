import styled from 'styled-components'

export const VirtualScrollContainer = styled.div<{ maxHeight: number }>`
  max-height: ${({ maxHeight }) => maxHeight}px;
  overflow-y: auto;
  overflow-x: hidden;
  /* Reserva espaço para a scrollbar para manter alinhamento */
  scrollbar-gutter: stable;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
`

export const VirtualContentContainer = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`

export const VirtualItemsWrapper = styled.div<{ translateY: number }>`
  transform: translateY(${({ translateY }) => translateY}px);
`

export const VirtualizedTableRow = styled.div<{ itemHeight: number }>`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; /* 6 colunas iguais, igual ao header */
  height: ${({ itemHeight }) => itemHeight}px;
  border-bottom: 1px solid ${({ theme }) => theme.legacy.colors.neutral.boxLabelAndContentLines};
  align-items: center;

  &:hover {
    background: ${({ theme }) => theme.legacy.colors.neutral.drops};
  }

  /* Remove a borda da última linha */
  &:last-child {
    border-bottom: none;
  }
`

// Célula do corpo da tabela
export const VirtualizedTableCell = styled.div`
  padding: 20px; /* Mesmo padding que o header e design original */
  text-align: left;
  font-size: 14px;
  font-weight: 500; /* Mesmo peso do design original */
  color: ${({ theme }) => theme.legacy.colors.grayScale.texts};
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`

export const VirtualizedTable = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`

// Wrapper principal usando CSS Grid para alinhamento perfeito
export const TableWrapper = styled.div`
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  /* Remove bordas externas para manter o design original */
  border: none;
  border-radius: 0;
  overflow: hidden;
  background-color: transparent;
`

// Header da tabela usando CSS Grid
export const TableHeaderWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; /* 6 colunas iguais */
  background-color: ${({ theme }) => theme.legacy.colors.neutral.contentBackground};
  /* Remove a borda inferior para manter o design original */
  border-bottom: none;

  /* Reserva espaço para a scrollbar */
  padding-right: 8px;
  box-sizing: border-box;
`

// Célula do header
export const TableHeaderCell = styled.div`
  padding: 20px; /* Mesmo padding do design original */
  color: ${({ theme }) => theme.legacy.colors.primary.primary};
  font-weight: 700;
  font-size: 14px;
  text-align: left;
  box-sizing: border-box;
  border: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`

// Container do corpo da tabela
export const TableBodyWrapper = styled.div`
  width: 100%;
  position: relative;
  overflow: hidden;
`

export const CenteredLoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  width: 100%;
`
