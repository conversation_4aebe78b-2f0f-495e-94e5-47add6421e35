import { useState, useCallback, useMemo } from 'react'
import { TPriceHistory } from 'hooks'
import { UseVirtualizationReturn, VirtualizationConfig } from './ticker-history.types'

export function useVirtualization(items: TPriceHistory[], config: VirtualizationConfig): UseVirtualizationReturn {
  const { itemHeight, maxVisibleItems } = config
  const [startIndex, setStartIndex] = useState(0)

  const shouldVirtualize = items.length > maxVisibleItems

  const visibleItems = useMemo(() => {
    if (!shouldVirtualize) return items

    const endIndex = Math.min(startIndex + maxVisibleItems, items.length)
    return items.slice(startIndex, endIndex)
  }, [items, startIndex, maxVisibleItems, shouldVirtualize])

  const containerMaxHeight = useMemo(() => {
    return maxVisibleItems * itemHeight
  }, [maxVisibleItems, itemHeight])

  const contentTotalHeight = useMemo(() => {
    return items.length * itemHeight
  }, [items.length, itemHeight])

  const itemsWrapperTranslateY = useMemo(() => {
    return startIndex * itemHeight
  }, [startIndex, itemHeight])

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop } = e.currentTarget
      const newStartIndex = Math.floor(scrollTop / itemHeight)
      setStartIndex(newStartIndex)
    },
    [itemHeight]
  )

  return {
    state: {
      startIndex,
      visibleItems,
      shouldVirtualize,
      containerMaxHeight,
      contentTotalHeight,
      itemsWrapperTranslateY,
    },
    actions: {
      handleScroll,
    },
  }
}
