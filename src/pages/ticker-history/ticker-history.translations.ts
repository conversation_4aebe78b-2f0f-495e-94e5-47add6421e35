import { i18n } from 'translate'

export const translations = {
  dataNotFound: i18n.t('tickerPriceHistory.DataNotFound'),
  startDateLabel: i18n.t('tickerPriceHistory.StartDateLabel'),
  endDateLabel: i18n.t('tickerPriceHistory.EndDateLabel'),
  tickerLabel: i18n.t('tickerPriceHistory.Ticker'),
  exportButton: i18n.t('tickerPriceHistory.Export'),
  exportSuccess: i18n.t('tickerPriceHistory.success'),
  exportMessage: i18n.t('tickerPriceHistory.exportMessage'),
  tableTitleDate: i18n.t('tickerPriceHistory.Date'),
  tableTitleOpeningPrice: i18n.t('tickerPriceHistory.OpeningPrice'),
  tableTitleClosingPrice: i18n.t('tickerPriceHistory.ClosingPrice'),
  tableTitleLowPrice: i18n.t('tickerPriceHistory.LowPrice'),
  tableTitleHighPrice: i18n.t('tickerPriceHistory.HighPrice'),
  tableTitleVolume: i18n.t('tickerPriceHistory.Volume'),
}
