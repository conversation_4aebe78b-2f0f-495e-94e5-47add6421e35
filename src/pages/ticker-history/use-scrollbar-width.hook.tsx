import { useEffect, useState } from 'react'

/**
 * Hook para detectar a largura da scrollbar do navegador
 * Isso é necessário para manter o alinhamento entre header e corpo da tabela
 */
export function useScrollbarWidth() {
  const [scrollbarWidth, setScrollbarWidth] = useState(0)

  useEffect(() => {
    const detectScrollbarWidth = () => {
      // Cria um elemento temporário para medir a scrollbar
      const outer = document.createElement('div')
      outer.style.visibility = 'hidden'
      outer.style.overflow = 'scroll'
      outer.style.msOverflowStyle = 'scrollbar' // Para IE
      document.body.appendChild(outer)

      const inner = document.createElement('div')
      outer.appendChild(inner)

      const scrollbarWidth = outer.offsetWidth - inner.offsetWidth
      outer.parentNode?.removeChild(outer)

      return scrollbarWidth
    }

    setScrollbarWidth(detectScrollbarWidth())
  }, [])

  return scrollbarWidth
}
